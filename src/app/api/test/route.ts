import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getToken } from "next-auth/jwt";
import { getDevAuthForAPI } from "@/lib/dev-auth";

const secret = process.env.NEXTAUTH_SECRET;

export async function GET(req: NextRequest, res: NextResponse) {
  // Check for development mode bypass
  const devAuth = await getDevAuthForAPI();
  if (devAuth) {
    return NextResponse.json(devAuth.jwt);
  }

  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json("Not logged in");
  }

  const token = await getToken({ req, secret });

  if (
    process.env.NODE_ENV != "development" &&
    process.env.ALEX_SETTING == "true"
  ) {
    throw new Error("NEVER IN PROD!!");
  }

  return NextResponse.json(token);
}
