import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";
import { getDevAuthForAPI } from "@/lib/dev-auth";

const secret = process.env.NEXTAUTH_SECRET;

// CREATE A MEDUSA JOB (Permissions are checked in the Server)
export async function POST(req: NextRequest, res: NextResponse) {
  // Check for development mode bypass
  const devAuth = await getDevAuthForAPI();
  let sesh = devAuth?.session || null;
  let token = devAuth?.jwt || null;

  if (!devAuth) {
    sesh = await getServerSession();
    if (!sesh) {
      return NextResponse.json({ error: "Need Log in" }, { status: 401 });
    }

    // It's guaranteed to be there due to the check above
    token = await getToken({ req, secret });
  }

  // For some reason we need to do this
  // Dumb af
  const body = await req.json();

  const {
    orgName,
    repoName,
    ref,
    directory,
    fuzzerArgs,
    preprocess,
    label,
    recipeId,
  } = body;
  let foundData;
  try {
    foundData = await axios({
      method: "POST",
      url: `${process.env.BACKEND_API_URL}/jobs/echidna`,
      headers: { Authorization: `Bearer ${token?.access_token as string}` },
      data: {
        orgName,
        repoName,
        ref,
        directory,
        fuzzerArgs,
        preprocess,
        label,
        recipeId,
      },
    });
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status }
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 }
      );
    }
  }

  // Returns an object with {data, message}
  return NextResponse.json(foundData.data);
}
