"use client";

import { SessionProvider } from "next-auth/react";
import { isDevelopmentMode, mockSession } from "@/lib/dev-auth";

export default function App({ children }: { children: React.ReactNode }) {
  // In development mode, provide a mock session
  if (isDevelopmentMode()) {
    return (
      <SessionProvider session={mockSession}>
        {children}
      </SessionProvider>
    );
  }

  // In production mode, use normal session provider
  return <SessionProvider>{children}</SessionProvider>;
}
