import type { JWT } from "next-auth/jwt";
import type { Session } from "next-auth";

/**
 * Development authentication bypass utility
 * Only active when NODE_ENV === "development"
 */

export const isDevelopmentMode = (): boolean => {
  return process.env.NODE_ENV === "development";
};

/**
 * Mock user data for development mode
 */
export const mockUser = {
  id: "dev-user-123",
  name: "Development User",
  email: "<EMAIL>",
  image: "https://avatars.githubusercontent.com/u/1?v=4",
  login: "dev-user",
};

/**
 * Mock JWT token for development mode
 */
export const mockJWT: JWT = {
  sub: mockUser.id,
  name: mockUser.name,
  email: mockUser.email,
  picture: mockUser.image,
  access_token: "dev-access-token",
  access_token_expires: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
  refresh_token: "dev-refresh-token",
  account: {
    provider: "github",
    type: "oauth",
    providerAccountId: mockUser.id,
    access_token: "dev-access-token",
    refresh_token: "dev-refresh-token",
    expires_at: Math.floor(Date.now() / 1000) + 3600,
    token_type: "bearer",
    scope: "read:user user:email",
  },
  profile: {
    id: mockUser.id,
    login: mockUser.login,
    name: mockUser.name,
    email: mockUser.email,
    avatar_url: mockUser.image,
    html_url: `https://github.com/${mockUser.login}`,
  },
};

/**
 * Mock session for development mode
 */
export const mockSession: Session = {
  user: {
    name: mockUser.name,
    email: mockUser.email,
    image: mockUser.image,
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
};

/**
 * Get development session if in development mode, otherwise return null
 */
export const getDevSession = (): Session | null => {
  if (!isDevelopmentMode()) {
    return null;
  }
  return mockSession;
};

/**
 * Get development JWT if in development mode, otherwise return null
 */
export const getDevJWT = (): JWT | null => {
  if (!isDevelopmentMode()) {
    return null;
  }
  return mockJWT;
};

/**
 * Check if authentication should be bypassed in development mode
 */
export const shouldBypassAuth = (): boolean => {
  return isDevelopmentMode();
};

/**
 * Development-aware session getter
 * Returns mock session in development, otherwise returns the provided session
 */
export const getSessionWithDevFallback = (
  session: Session | null
): Session | null => {
  if (isDevelopmentMode() && !session) {
    return mockSession;
  }
  return session;
};

/**
 * Development-aware JWT getter
 * Returns mock JWT in development, otherwise returns the provided JWT
 */
export const getJWTWithDevFallback = (jwt: JWT | null): JWT | null => {
  if (isDevelopmentMode() && !jwt) {
    return mockJWT;
  }
  return jwt;
};

/**
 * API route authentication helper for development mode
 * Returns mock session and JWT in development, otherwise returns actual values
 */
export const getDevAuthForAPI = async () => {
  if (isDevelopmentMode()) {
    return {
      session: mockSession,
      jwt: mockJWT,
      isAuthenticated: true,
    };
  }
  return null;
};
